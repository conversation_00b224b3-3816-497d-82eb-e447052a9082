import React from "react";
import { Button } from "../../components/ui/button";
import { Card } from "../../components/ui/card";
import { DivSubsection } from "./sections/DivSubsection";
import { DivWrapperSubsection } from "./sections/DivWrapperSubsection";
import { FrameSubsection } from "./sections/FrameSubsection";
import { FrameWrapperSubsection } from "./sections/FrameWrapperSubsection";
import { PaniKasiaToSubsection } from "./sections/PaniKasiaToSubsection";
import { SectionComponentNodeSubsection } from "./sections/SectionComponentNodeSubsection";
import { ZajCiaZKasiOkazaSubsection } from "./sections/ZajCiaZKasiOkazaSubsection/ZajCiaZKasiOkazaSubsection";

export const MainPage = (): JSX.Element => {
  // Testimonial data for reusability
  const testimonials = [
    {
      id: 1,
      content:
        "Bardzo polecam zajęcia z Panią Kasią, dzięki nim udało mi się zdać egzamin językowy na studiach, a mój angielski się poprawił. Kasia jest świetną nauczycielką, bardzo pomocną i wyrozumiałą. Zajęcia odbywały się w świetnej atmosferze.",
      author: "Andżelika",
      bgColor: "bg-[#66caff33]",
      iconBgColor: "bg-[#144969]",
    },
    {
      id: 2,
      content:
        "Chciałbym serdecznie polecić panią Kasie . Dzięki jego profesjonalnemu podejściu i zaangażowaniu, byłem świetnie przygotowany do matury. Lekcje były interesujące i dobrze zorganizowane, a materiały dydaktyczne bardzo pomocne. Nauczyciel zawsze był cierpliwy i gotowy do pomocy. Dzięki niemu zdobyłem nie tylko wiedzę, ale i pewność siebie. Gorąco polecam!",
      author: "Dawid",
      bgColor: "bg-[#66caff33]",
      iconBgColor: "bg-[#162343]",
    },
    {
      id: 3,
      content:
        "Dostałam dofinansowanie z firmy na rozwijanie języka angielskiego. Pierwszy raz czuję, że mam szansę nauczyć się tego języka z Panią Kasią. Uważam, że stosuje genialną metodę wracania do materiału i powtarzania bloków tematycznych po kilka razy ale w różnorodny i ciekawy sposób. Ja jestem zachwycona!",
      author: "Agnieszka",
      bgColor: "bg-[#9ee75d33]",
      iconBgColor: "bg-[#9ee75d]",
    },
  ];

  return (
    <div className="bg-white min-h-screen">
      <div className="w-full max-w-7xl mx-auto">
        {/* Hero Section */}
        <FrameSubsection />
        
        {/* About Section */}
        <div className="px-4 md:px-8 lg:px-20">
          <FrameWrapperSubsection />
        </div>
        
        {/* Services Section */}
        <div className="px-4 md:px-8 lg:px-20">
          <DivWrapperSubsection />
        </div>
        
        {/* Pricing Section */}
        <div className="px-4 md:px-8 lg:px-20">
          <DivSubsection />
        </div>

        {/* Testimonials Section */}
        <section className="px-4 md:px-8 lg:px-20 py-16">
          <h2 className="font-['Montserrat',Helvetica] font-bold text-black text-[32px] mb-12">
            Opinie
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl">
            {/* Left Column - Testimonials */}
            <div className="space-y-8">
              {testimonials.map((testimonial) => (
                <Card
                  key={testimonial.id}
                  className="border-none shadow-lg rounded-[20px] overflow-hidden"
                >
                  <div className={`${testimonial.bgColor} p-6`}>
                    <div className="flex items-start gap-4">
                      <div
                        className={`w-[107px] h-[105px] ${testimonial.iconBgColor} rounded-[0px_15px_0px_15px] flex items-center justify-center flex-shrink-0`}
                      >
                        <img
                          className="w-[69px] h-[43px]"
                          alt="Quote icon"
                          src="/tryb-izolacji-2.svg"
                        />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        {testimonial.id === 1 && <PaniKasiaToSubsection />}
                        {testimonial.id === 3 && <ZajCiaZKasiOkazaSubsection />}
                        {testimonial.id === 2 && (
                          <div className="font-['Poppins',Helvetica] font-normal text-black text-base">
                            <p className="mb-4">{testimonial.content}</p>
                            <p className="font-light italic">{testimonial.author}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Right Column - Decorative Elements */}
            <div className="hidden lg:flex items-center justify-center">
              <div className="relative w-full max-w-md">
                <img
                  className="w-full h-auto opacity-20"
                  alt="Decorative illustration"
                  src="/group-25.png"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <SectionComponentNodeSubsection />

        {/* Floating Action Button */}
        <Button
          className="fixed bottom-8 right-8 w-[52px] h-[52px] rounded-[5px] p-0 shadow-lg z-50"
          variant="outline"
        >
          <img className="w-[15px] h-6" alt="Arrow" src="/arrow-1.svg" />
        </Button>
      </div>
    </div>
  );
};