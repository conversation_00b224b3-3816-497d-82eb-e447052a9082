import React from "react";
import { Card, CardContent } from "../../../../components/ui/card";

export const ZajCiaZKasiOkazaSubsection = (): JSX.Element => {
  return (
    <Card className="w-full max-w-[445px] font-['Poppins',Helvetica] text-base">
      <CardContent className="pt-6">
        <p className="font-normal text-black">
          Zajęcia z Kasią okazały się dla mnie wybawieniem. Bardzo długo tkwiłam
          w szkolnej pułapce - rozumiałam mniej więcej teksty, jednak nie
          potrafiłam wykrztusić z siebie pół słowa w języku angielskim, bałam
          się oceny innych ludzi, miałam blokadę językową. Z Kasią
          współpracujemy już rok, a ja widzę efekty - nie boję się używać języka
          angielskiego w kontaktach z innymi ludźmi, potrafię swobodnie się
          wypowiadać, jestem w stanie oglądać po angielsku filmy i seriale.
          Ponadto zajęcia prowadzone są w świetnej atmosferze, a nauka gramatyki
          to przyjemność. Zajęcia są dostosowane do mnie oraz moich potrzeb.
          Materiały, które Kasia oferuje, są dostosowane do potrzeb, ale też
          zainteresowań kursanta, dlatego nauka jest tak przyjemna. Z całego
          serca polecam Kasię, jest świetna w tym co robi, ma indywidualne
          podejście do kursanta, a zajęcia z nią to czysta przyjemność!
        </p>

        <div className="mt-6 font-light italic">Julia</div>
      </CardContent>
    </Card>
  );
};
