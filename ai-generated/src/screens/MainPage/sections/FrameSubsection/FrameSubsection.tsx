import React from "react";
import { Button } from "../../../../components/ui/button";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from "../../../../components/ui/navigation-menu";

export const FrameSubsection = (): JSX.Element => {
  // Navigation menu items
  const navItems = [
    { label: "Oferta zajęć" },
    { label: "Cennik" },
    { label: "Opinie" },
    { label: "Sklep" },
    { label: "Po godzinach" },
    { label: "Kontakt" },
  ];

  // Social media icons
  const socialIcons = [
    {
      src: "/group-22.png",
      alt: "Group",
      className: "w-[31px] h-[31px]",
    },
    {
      src: "/tryb-izolacji-5.svg",
      alt: "Tryb izolacji",
      className: "w-[31px] h-[31px]",
    },
    { 
      src: "/tryb-izolacji-4.svg", 
      alt: "Social media",
      className: "w-8 h-[31px]" 
    },
  ];

  return (
    <section className="relative w-full min-h-screen overflow-hidden bg-gradient-to-b from-white to-gray-50">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-10 pointer-events-none">
        <img
          className="absolute top-0 right-0 w-1/2 h-auto max-w-2xl"
          alt="Background decoration"
          src="/group.png"
        />
        <img
          className="absolute top-1/4 right-0 w-2/3 h-auto max-w-3xl"
          alt="Background decoration"
          src="/group-1.png"
        />
      </div>

      {/* Header */}
      <header className="relative z-10 w-full">
        <div className="flex flex-col lg:flex-row items-center justify-between px-4 md:px-8 lg:px-20 py-6">
          {/* Logo */}
          <div className="mb-4 lg:mb-0">
            <img
              className="w-32 h-auto lg:w-[169px]"
              alt="Logo azpk"
              src="/logo-azpk-przezroczystetlo-1.png"
            />
          </div>

          {/* Navigation - Hidden on mobile, shown on desktop */}
          <NavigationMenu className="hidden lg:block">
            <NavigationMenuList className="flex items-center gap-6">
              {navItems.map((item, index) => (
                <NavigationMenuItem key={index}>
                  <NavigationMenuLink className="font-['Montserrat',Helvetica] font-normal text-black text-base hover:text-blue-600 transition-colors">
                    {item.label}
                  </NavigationMenuLink>
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>

          {/* CTA Button and Social Icons */}
          <div className="flex flex-col sm:flex-row items-center gap-4">
            <Button className="w-full sm:w-auto px-6 py-3 rounded-[20px] bg-[linear-gradient(97deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)] hover:bg-[linear-gradient(97deg,rgba(28,169,226,0.9)_0%,rgba(115,183,55,0.9)_100%)] font-['Montserrat',Helvetica] font-bold text-white text-sm lg:text-base">
              MATERIAŁY DO POBRANIA
            </Button>

            <div className="flex items-center gap-3">
              {socialIcons.map((icon, index) => (
                <img 
                  key={index}
                  className={`${icon.className} hover:opacity-80 transition-opacity cursor-pointer`} 
                  alt={icon.alt} 
                  src={icon.src} 
                />
              ))}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Content */}
      <div className="relative z-10 flex flex-col lg:flex-row items-center min-h-[calc(100vh-200px)] px-4 md:px-8 lg:px-20 py-12">
        {/* Left side - Text content */}
        <div className="w-full lg:w-1/2 mb-12 lg:mb-0">
          <h1 className="font-['Montserrat',Helvetica] font-bold text-black text-3xl md:text-4xl lg:text-5xl leading-tight mb-8">
            Poznaj nowy wymiar <br />
            nauki języka angielskiego
          </h1>

          <Button className="px-8 py-4 rounded-[20px] bg-[linear-gradient(97deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)] hover:bg-[linear-gradient(97deg,rgba(28,169,226,0.9)_0%,rgba(115,183,55,0.9)_100%)] font-['Montserrat',Helvetica] font-bold text-white text-base">
            DOWIEDZ SIĘ WIĘCEJ
          </Button>
        </div>

        {/* Right side - Illustration */}
        <div className="w-full lg:w-1/2 flex justify-center lg:justify-end">
          <div className="relative max-w-md lg:max-w-lg">
            <img
              className="w-full h-auto"
              alt="English learning illustration"
              src="/frame-9.svg"
            />
            
            {/* Floating elements */}
            <div className="absolute top-4 right-4 w-12 h-8 opacity-80">
              <img
                className="w-full h-full"
                alt="Flag decoration"
                src="/vector-29.svg"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom gradient overlay */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white to-transparent pointer-events-none" />
    </section>
  );
};