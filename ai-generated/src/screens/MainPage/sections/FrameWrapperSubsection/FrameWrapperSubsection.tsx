import React from "react";
import { Card, CardContent } from "../../../../components/ui/card";

export const FrameWrapperSubsection = (): JSX.Element => {
  return (
    <section className="w-full py-12 px-4 md:px-8 lg:px-20">
      <h2 className="font-bold text-black text-[32px] font-['Montserrat',Helvetica] mb-8">
        <PERSON> jeste<PERSON>?
      </h2>

      <div className="flex flex-col md:flex-row gap-8">
        <div className="w-full md:w-[323px] flex-shrink-0">
          <img
            className="w-full h-auto max-w-[323px]"
            alt="Frame"
            src="/frame-35.svg"
          />
        </div>

        <Card className="border-none shadow-none">
          <CardContent className="p-0 font-['Montserrat',Helvetica] text-base">
            <p className="text-black">
              Ukończyłam filologię angielską i prawo na UW. Mam duże
              doświadczenie jako lektor d<PERSON>, m<PERSON><PERSON><PERSON><PERSON><PERSON>, studentów i
              dorosłych. Najbardziej lubię uczyć indywidualnie, tak, aby osoba
              mówiła swobodnie o rzeczach, które ją interesują i są dla niej czy
              dla niego ważne.{" "}
            </p>

            <p className="font-bold text-[#73b737]">
              Przygotowuję też do egzaminu ósmoklasisty, matur oraz certyfikatu
              First B2, C1 Advanced czy B2 UW
              <br />
            </p>

            <p className="text-black">
              .<br />
              Ze swojej strony oferuję przede wszystkim cierpliwość,
              profesjonalizm, przyjazną atmosferę i entuzjastyczne podejście.
              Zajęcia są zawsze dobrze przygotowane (materiały w pakiecie) i
              interesujące. Lubię używać gier i materiałów wizualnych.
              <br />
              <br />
            </p>

            <p>
              <span className="font-bold text-[#1ca9e2]">Co Cię czeka? -</span>
              <span className="text-black">
                {" "}
                Więcej niż dobra lekcja. Zero stresu i nudy! :) Niech nauka, a w
                szczególności mówienie, będzie przyjemnością!
                <br />
              </span>
            </p>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};
