import React from "react";
import { Card, CardContent } from "../../../../components/ui/card";

export const PaniKasiaToSubsection = (): JSX.Element => {
  return (
    <Card className="w-full max-w-[461px] mt-32 mx-auto font-['Poppins',Helvetica]">
      <CardContent className="pt-6">
        <blockquote className="text-base">
          <p className="mb-6">
            <PERSON><PERSON> to absolutnie najlepsza nauczycielka języka angielskiego,
            jaką miałem. Jej lekcje są zawsze starannie przygotowane i pełne
            interesujących materiałów. Potrafi świetnie wyjaśnić nawet
            najbardziej skomplikowane zagadnienia, a jej cierpliwość i
            zaangażowanie sprawiają, że nauka staje się przyjemnością. Dzięki
            niej szybko poprawiłem swoje umiejętności językowe. Gorąco polecam!
          </p>
          <footer className="font-light italic"><PERSON></footer>
        </blockquote>
      </CardContent>
    </Card>
  );
};
