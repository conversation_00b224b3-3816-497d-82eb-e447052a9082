import React from "react";
import { Button } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";

export const DivSubsection = (): JSX.Element => {
  // Define service cards data for mapping
  const serviceCards = [
    {
      title: "General \nEnglish",
      price: "150 PLN / 55 min",
      color: "#73b737",
      description: [
        "konwersacje dla dzieci, młodzie<PERSON><PERSON>",
        "i dorosłych",
        "",
        "bieżąca pomoc w nauce",
        "",
        "przygotowanie do egzaminów",
        "i certyfikatów",
      ],
    },
    {
      title: "Law / Business\nEnglish",
      price: "165 PLN / 55 min",
      color: "#1ca9e2",
      description: [
        "Korespondencja i rozmowy",
        "telefoniczne w biznesie",
        "",
        "Terminologia prawnicza",
        "i ekonomiczna",
        "",
        "Prezentacje i nego<PERSON><PERSON><PERSON><PERSON>",
        "biznesowe",
      ],
    },
    {
      title: "Konsultacje\nIndywidualne",
      price: "350 PLN / 165 min",
      color: "#73b737",
      description: [
        "Pakiet 3 spotkań po 55 min",
        "",
        "przygotowanie CV i listu",
        "motywacyjnego",
        "",
        "wpisu w mediach społecznościowych",
        "",
        "rozmowa o pracę",
      ],
    },
    {
      title: "Tłumaczenia",
      price: "stawka ustalana \nindywidualnie",
      color: "#1ca9e2",
      description: [
        "Korespondencja",
        "",
        "Prezentacje, oferty, katalogi",
        "i instrukcje",
        "",
        "Pisma z elementami terminologii",
        "prawniczej i ekonomicznej",
        "",
        "Strony internetowe",
      ],
    },
  ];

  return (
    <section className="w-full py-16">
      <h2 className="text-[32px] font-bold [font-family:'Montserrat',Helvetica] text-black mb-16">
        Cennik
      </h2>

      {/* Background decoration */}
      <div className="relative mb-16">
        <div className="absolute inset-0 flex justify-center opacity-20 pointer-events-none">
          <img
            className="w-full max-w-5xl h-auto"
            alt="Background decoration"
            src="/group-23.png"
          />
        </div>

        {/* Service Cards Grid */}
        <div className="relative z-10 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 max-w-7xl mx-auto">
          {serviceCards.map((card, index) => (
            <Card
              key={index}
              className="h-full rounded-[20px] border-2 border-solid hover:shadow-lg transition-shadow duration-300"
              style={{ borderColor: card.color }}
            >
              <CardContent className="p-6 flex flex-col h-full">
                <h3
                  className="mt-4 mb-6 [font-family:'Montserrat',Helvetica] font-bold text-[28px] lg:text-[32px] text-center tracking-[0] leading-normal whitespace-pre-line"
                  style={{ color: card.color }}
                >
                  {card.title}
                </h3>

                <div className="mb-6 [font-family:'Montserrat',Helvetica] font-semibold text-black text-xl lg:text-2xl text-center tracking-[0] leading-normal whitespace-pre-line">
                  {card.price}
                </div>

                <div className="flex-grow mb-6 [font-family:'Montserrat',Helvetica] font-normal text-black text-sm lg:text-base text-center tracking-[0] leading-[18px]">
                  {card.description.map((line, i) => (
                    <React.Fragment key={i}>
                      {line}
                      <br />
                    </React.Fragment>
                  ))}
                </div>

                <Button className="w-full mt-auto rounded-[20px] bg-[linear-gradient(90deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)] hover:bg-[linear-gradient(90deg,rgba(28,169,226,0.9)_0%,rgba(115,183,55,0.9)_100%)] [font-family:'Montserrat',Helvetica] font-bold text-white text-base">
                  KONTAKT
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Additional Info */}
      <div className="w-full max-w-4xl mx-auto [font-family:'Montserrat',Helvetica] font-normal text-black text-base text-center tracking-[0] leading-normal px-4">
        Istnieje możliwość wykupienia pakietu lekcji [5 po 55 min, 10 po 55 min].
        <br />
        Prowadzę zajęcia z dofinansowaniem od firmy dla pracownika, wystawiam faktury.
      </div>
    </section>
  );
};