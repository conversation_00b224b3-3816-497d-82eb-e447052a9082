import React from "react";

export const SectionComponentNodeSubsection = (): JSX.Element => {
  const socialIcons = [
    {
      alt: "Tryb izolacji",
      src: "/tryb-izolacji-5.svg",
      className: "w-[30px] h-[30px]",
    },
    {
      alt: "Social media icon",
      src: "/tryb-izolacji-4.svg",
      className: "w-8 h-[31px]",
    },
  ];

  return (
    <footer className="w-full bg-gray-900 py-8 mt-16">
      <div className="max-w-7xl mx-auto px-4 md:px-8 lg:px-20">
        <div className="flex flex-col md:flex-row justify-between items-center gap-6">
          {/* Left side - Copyright and Privacy */}
          <div className="flex flex-col items-center md:items-start gap-2 text-center md:text-left">
            <p className="font-['Montserrat',Helvetica] font-normal text-white text-base">
              © Katarzyna <PERSON> 2025
            </p>

            <a
              className="font-['Montserrat',Helvetica] font-normal text-white text-base underline hover:text-gray-300 transition-colors"
              href="https://www.katarzynatyszkiewicz.pl/polityka-prywatnosci/"
              rel="noopener noreferrer"
              target="_blank"
            >
              Polityka Prywatności
            </a>
          </div>

          {/* Center - Attribution */}
          <div className="font-['Montserrat',Helvetica] font-normal text-white text-base text-center">
            <span>Dumnie wspierane przez </span>
            <a
              href="https://moondev.net/"
              rel="noopener noreferrer"
              target="_blank"
              className="underline hover:text-gray-300 transition-colors"
            >
              Moondev.net
            </a>
            <span>.</span>
          </div>

          {/* Right side - Social Icons */}
          <div className="flex items-center gap-3">
            {socialIcons.map((icon, index) => (
              <img
                key={`social-icon-${index}`}
                className={`${icon.className} hover:opacity-80 transition-opacity cursor-pointer`}
                alt={icon.alt}
                src={icon.src}
              />
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};