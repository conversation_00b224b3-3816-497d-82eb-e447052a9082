import React from "react";
import { Button } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";

export const DivWrapperSubsection = (): JSX.Element => {
  // Course offerings data
  const courseOfferings = [
    {
      id: 1,
      title: "Język angielski ogólny \noraz konwersacje",
      titleColor: "text-[#325118]",
      borderColor: "border-[#73b737]",
      imageSrc: "/frame-47.svg",
      imagePosition: "left",
      buttonGradient:
        "bg-[linear-gradient(90deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)]",
      features: [
        {
          text: (
            <span className="font-bold">
              Ćwiczenie 4 najważniejszych sprawności: <br />
            </span>
          ),
          subtext: "mówienie, pisanie, czytanie i słuchanie",
        },
        { text: "Utrwalanie struktur gramatycznych i nauka słownictwa" },
        { text: "Praca nad wymową" },
        { text: "EFEKT: SWOBODA KOMUNIKACYJNA", isBold: true },
      ],
      hasCheckboxes: false,
    },
    {
      id: 2,
      title: "Język angielski w biznesie",
      titleColor: "text-[#005393]",
      borderColor: "border-[#1ca9e2]",
      imageSrc: "/frame-48.svg",
      imagePosition: "right",
      buttonGradient:
        "bg-[linear-gradient(90deg,rgba(115,183,55,1)_0%,rgba(28,169,226,1)_100%)]",
      subtitle: "General English",
      features: [
        { text: "CV i list motywacyjny" },
        { text: "Prezentacje i negocjacje" },
        { text: "Korespondencja i rozmowy telefoniczne" },
        { text: "Terminologia prawnicza i ekonomiczna" },
      ],
      checkboxGradient:
        "bg-[linear-gradient(180deg,rgba(28,169,226,1)_0%,rgba(15,93,124,1)_100%)]",
      hasCheckboxes: true,
    },
    {
      id: 3,
      title: "Język angielski \ndla dzieci i młodzieży",
      titleColor: "text-[#325118]",
      borderColor: "border-[#73b737]",
      imageSrc: "/frame-49.svg",
      imagePosition: "left",
      buttonGradient:
        "bg-[linear-gradient(90deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)]",
      features: [
        { text: "Bieżąca pomoc w lekcjach i przygotowanie do sprawdzianów" },
        { text: "Język angielski funkcyjny i sytuacyjny" },
        { text: "Gry i zabawy językowe" },
        {
          text: "4 sprawności (mówienie, pisanie, czytanie i słuchanie) + wymowa",
        },
        { text: "Gramatyka i słownictwo" },
        { text: "Misja egzamin ósmoklasisty / Matura" },
      ],
      checkboxGradient:
        "bg-[linear-gradient(180deg,rgba(115,183,55,1)_0%,rgba(51,81,24,1)_100%)]",
      hasCheckboxes: true,
    },
    {
      id: 4,
      title: "Język angielski do egzaminów",
      titleColor: "text-[#005393]",
      borderColor: "border-[#1ca9e2]",
      imageSrc: "/frame-50.svg",
      imagePosition: "right",
      buttonGradient:
        "bg-[linear-gradient(90deg,rgba(115,183,55,1)_0%,rgba(28,169,226,1)_100%)]",
      subtitle: "General English",
      subheader: "Przygotowanie do:",
      features: [
        { text: "Egzaminy ósmoklasisty" },
        { text: "Matury" },
        { text: "Certyfikatów B2 First (FCE) i C1 Advanced (CAE)" },
        { text: "Certyfikatu B2 UW" },
      ],
      checkboxGradient:
        "bg-[linear-gradient(180deg,rgba(28,169,226,1)_0%,rgba(15,93,124,1)_100%)]",
      hasCheckboxes: true,
    },
    {
      id: 5,
      title: "Tłumaczenia",
      titleColor: "text-[#325118]",
      borderColor: "border-[#73b737]",
      imageSrc: "/frame-51.svg",
      imagePosition: "left",
      buttonGradient:
        "bg-[linear-gradient(90deg,rgba(28,169,226,1)_0%,rgba(115,183,55,1)_100%)]",
      features: [
        { text: "Korespondencja" },
        { text: "CV i list motywacyjny" },
        { text: "Prezentacje, oferty, katalogi i instrukcje" },
        { text: "Strony internetowe" },
        {
          text: "Pisma z elementami terminologii prawniczej i ekonomicznej itp.",
        },
      ],
      checkboxGradient:
        "bg-[linear-gradient(180deg,rgba(115,183,55,1)_0%,rgba(51,81,24,1)_100%)]",
      hasCheckboxes: true,
    },
  ];

  return (
    <section className="w-full py-8">
      <h2 className="font-bold [font-family:'Montserrat',Helvetica] text-black text-[32px] mb-8">
        Oferta zajęć
      </h2>

      <div className="flex flex-col gap-8">
        {courseOfferings.map((course) => (
          <Card
            key={course.id}
            className={`w-full rounded-[20px] border-2 border-solid ${course.borderColor} backdrop-blur-[8.4px] backdrop-brightness-[100%] overflow-hidden`}
          >
            <CardContent className="p-0 relative flex flex-row h-full min-h-[400px]">
              {course.imagePosition === "left" ? (
                <div className="w-[530px] flex-shrink-0 p-12">
                  <img
                    className="w-full h-auto"
                    alt="Course illustration"
                    src={course.imageSrc}
                  />
                </div>
              ) : null}

              <div className="flex-grow p-12 flex flex-col">
                <h3
                  className={`[font-family:'Montserrat',Helvetica] font-bold ${course.titleColor} text-4xl mb-6 whitespace-pre-line`}
                >
                  {course.title}
                </h3>

                {course.subtitle && (
                  <h4 className="[font-family:'Montserrat',Helvetica] font-bold text-black text-xl mb-4">
                    {course.subtitle}
                  </h4>
                )}

                {course.subheader && (
                  <p className="[font-family:'Montserrat',Helvetica] font-normal text-black text-xl mb-4">
                    {course.subheader}
                  </p>
                )}

                <div className="flex flex-col gap-3 mb-auto">
                  {course.features.map((feature, index) => (
                    <div key={index} className="flex items-start gap-3">
                      {course.hasCheckboxes && (
                        <div
                          className={`w-[17px] h-[17px] rounded-sm flex-shrink-0 mt-1.5 ${course.checkboxGradient}`}
                        />
                      )}
                      <p
                        className={`[font-family:'Montserrat',Helvetica] ${feature.isBold ? "font-bold" : "font-normal"} text-black text-xl`}
                      >
                        {feature.text}
                        {feature.subtext && (
                          <span className="[font-family:'Montserrat',Helvetica] font-normal text-black text-xl">
                            {feature.subtext}
                          </span>
                        )}
                      </p>
                    </div>
                  ))}
                </div>

                <Button
                  className={`mt-8 w-[207px] rounded-[20px] ${course.buttonGradient} text-white [font-family:'Montserrat',Helvetica] font-bold`}
                >
                  CENNIK
                </Button>
              </div>

              {course.imagePosition === "right" ? (
                <div className="w-[530px] flex-shrink-0 p-12">
                  <img
                    className="w-full h-auto"
                    alt="Course illustration"
                    src={course.imageSrc}
                  />
                </div>
              ) : null}
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};
